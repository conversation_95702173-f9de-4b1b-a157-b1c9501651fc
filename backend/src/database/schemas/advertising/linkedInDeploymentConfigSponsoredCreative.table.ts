import { decimal, integer, numeric } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInDeploymentConfigTable } from "./linkedInDeploymentConfig.table";
import { linkedInSponsoredCreativeTable } from "./linkedInSponsoredCreative.table";

export const linkedInDeploymentConfigSponsoredCreative = adSchema.table(
  "linkedin_deployment_config_sponsored_creative",
  {
    id: uuIdCol,
    linkedInDeploymentConfigId: uuIdFk("linkedin_deployment_config_id")
      .notNull()
      .references(() => linkedInDeploymentConfigTable.id),
    sponsoredCreativeId: uuIdFk("sponsored_creative_id")
      .notNull()
      .references(() => linkedInSponsoredCreativeTable.id),
    impressions: integer("impressions"),
    clicks: integer("clicks"),
    conversions: integer("conversions"),
    cost: numeric("cost", { precision: 12, scale: 2 }),
    videoViews: integer("video_views"),
    sends: integer("sends"),
    opens: integer("opens"),
    leads: integer("leads"),
    actionClicks: integer("action_clicks"),
    totalEngagements: integer("total_engagements"),
    oneClickLeadFormOpens: integer("one_click_lead_form_opens"),
    landingPageClicks: integer("landing_page_clicks"),
    videoCompletions: integer("video_completions"),
    videoFirstQuartileCompletions: integer("video_first_quartile_completions"),
    videoMidpointCompletions: integer("video_midpoint_completions"),
    videoThirdQuartileCompletions: integer("video_third_quartile_completions"),
    videoStarts: integer("video_starts"),
    externalWebsiteConversions: integer("external_website_conversions"),
  },
);
