import { text, unique, varchar } from "drizzle-orm/pg-core";

import { ConversationSubjectCopy } from "../../../modules/advertising/domain/entites/conversationSubjectCopy";
import { linkedInUrnCol, uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { conversationSubjectCopyTypeTable } from "./conversationSubjectCopyType.table";
import { linkedInAdSegmentValuePropTable } from "./linkedInAdSegmentValueProp.table";

export const conversationSubjectCopyTable = adSchema.table(
  "conversation_subject_copy",
  {
    id: uuIdCol,
    adSegmentValuePropId: uuIdFk("ad_segment_value_prop_id")
      .notNull()
      .references(() => linkedInAdSegmentValuePropTable.id),
    content: text("content").notNull(),
    type: varchar("type", { length: 255 }).notNull(),
    leadGenFormUrn: linkedInUrnCol("lead_gen_form_urn"),
    status: varchar("status", { length: 255 })
      .default("DRAFT")
      .notNull()
      .$type<"DRAFT" | "ACTIVE" | "ARCHIVED">(),
  },
  (t) => ({
    uniqueSubjectCopyPerAdSegmentValueProp: unique(
      "unique_subject_copy_per_ad_segment_value_prop",
    ).on(t.adSegmentValuePropId, t.type),
  }),
);
