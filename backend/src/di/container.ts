// di-container.ts
type Factory<T> = (container: Container) => T;

export class Container {
  private factories = new Map<symbol, Factory<any>>();
  private instances = new Map<symbol, any>();

  // Register a factory for a token
  register<T>(token: symbol & { __type?: T }, factory: Factory<T>): void {
    this.factories.set(token, factory);
  }

  // Resolve a dependency (lazy initialization)
  resolve<T>(token: symbol & { __type?: T }): T {
    // Return cached instance if exists
    if (this.instances.has(token)) {
      return this.instances.get(token);
    }

    // Get factory
    const factory = this.factories.get(token);
    if (!factory) {
      throw new Error(`No factory registered for token: ${String(token)}`);
    }

    // Create instance
    const instance = factory(this);
    this.instances.set(token, instance);
    return instance;
  }

  // Clear instances (useful for testing)
  clear(): void {
    this.instances.clear();
  }
}

// Create type-safe tokens
export function createToken<T>(name: string): symbol & { __type?: T } {
  return Symbol(name) as symbol & { __type?: T };
}

// tokens.ts
export const Tokens = {
  AbTestRepository: createToken<IAbTestRepository>("AbTestRepository"),
  AutoResolveAbTestCommandHandler: createToken<AutoResolveAbTestCommandHandler>(
    "AutoResolveAbTestCommandHandler",
  ),
};

const container = new Container();

container.regisoer(Tokens.AutoResolveAbTestCommandHandler, (c) =>
  createAutoResolveAbTestCommandhandler({
    abTestRepository: c.resolve(Tokens.AbTestRepository),
    // other dependencies
  }),
);

// usage.ts
// Type-safe resolution
const manager = container.resolve(Tokens.AutoResolveAbTestCommandHandler);
manager.execute({
  abTestId: "123",
  abTestType: "audience",
  varientId: "123",
});
