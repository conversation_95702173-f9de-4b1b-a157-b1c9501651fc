import { and, eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { conversationCallToActionCopyTable } from "../../../../database/schemas/advertising/conversationCallToActionCopy.table";
import { conversationMessageCopyTable } from "../../../../database/schemas/advertising/conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "../../../../database/schemas/advertising/conversationSubjectCopy.table";
import { linkedInAdSegmentValuePropTable } from "../../../../database/schemas/advertising/linkedInAdSegmentValueProp.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IConversationCallToActionCopyRepository } from "../../application/interfaces/infrastructure/repositories/conversationCallToActionCopy.repository.interface";
import { ConversationCallToActionCopy } from "../../domain/entites/conversationCallToActionCopy";
import { ConversationMessageCopy } from "../../domain/entites/conversationMessageCopy";
import { ConversationSubjectCopy } from "../../domain/entites/conversationSubjectCopy";
import { ConversationCallToActionCopyType } from "../../domain/valueObjects/conversationCallToActionCopyType";
import { ConversationMessageCopyType } from "../../domain/valueObjects/conversationMessageCopyType";
import { ConversationSubjectCopyType } from "../../domain/valueObjects/conversationSubjectCopyType";

export class ConversationCallToActionCopyRepository
  implements IConversationCallToActionCopyRepository
{
  async createOrUpdateOneIfExists(
    conversationCallToActionCopy: Omit<
      ConversationCallToActionCopy,
      "conversationMessageCopyId" | "conversationSubjectCopyId"
    >,
    tx?: Transaction,
  ): Promise<ConversationCallToActionCopy> {
    const invoker = tx ?? db;
    const conversationMessageCopyId = (
      await invoker
        .select({
          id: conversationMessageCopyTable.id,
        })
        .from(conversationMessageCopyTable)
        .innerJoin(
          conversationSubjectCopyTable,
          eq(
            conversationMessageCopyTable.conversationSubjectCopyId,
            conversationSubjectCopyTable.id,
          ),
        )
        .where(
          and(
            eq(
              conversationMessageCopyTable.type,
              conversationCallToActionCopy.messageType,
            ),
            eq(
              conversationSubjectCopyTable.type,
              conversationCallToActionCopy.subjectType,
            ),
            eq(
              conversationSubjectCopyTable.adSegmentValuePropId,
              conversationCallToActionCopy.valuePropId,
            ),
          ),
        )
    )[0]?.id;
    if (!conversationMessageCopyId) {
      throw new Error("Conversation message copy not found");
    }
    await invoker
      .insert(conversationCallToActionCopyTable)
      .values({
        id: conversationCallToActionCopy.id,
        conversationMessageCopyId,
        content: conversationCallToActionCopy.content,
        type: conversationCallToActionCopy.type,
        status: conversationCallToActionCopy.status,
      })
      .onConflictDoUpdate({
        target: [
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationCallToActionCopyTable.type,
        ],
        set: {
          content: conversationCallToActionCopy.content,
        },
      });
    return {
      id: conversationCallToActionCopy.id,
      conversationMessageCopyId,
      conversationSubjectCopyId: conversationMessageCopyId,
      content: conversationCallToActionCopy.content,
      type: conversationCallToActionCopy.type,
      subjectType: conversationCallToActionCopy.subjectType,
      valuePropId: conversationCallToActionCopy.valuePropId,
      messageType: conversationCallToActionCopy.messageType,
      status: conversationCallToActionCopy.status,
    };
  }
  async getOne(
    input: {
      valuePropId: string;
      conversationMessageCopyType: ConversationMessageCopyType;
      conversationSubjectCopyType: ConversationSubjectCopyType;
      conversationCallToActionCopyType: ConversationCallToActionCopyType;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<ConversationCallToActionCopy | null> {
    const invoker = tx ?? db;
    const conversationCallToActionCopy = await invoker
      .select()
      .from(conversationCallToActionCopyTable)
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(
            conversationCallToActionCopyTable.type,
            input.conversationCallToActionCopyType,
          ),
          eq(
            conversationMessageCopyTable.type,
            input.conversationMessageCopyType,
          ),
          eq(
            conversationSubjectCopyTable.type,
            input.conversationSubjectCopyType,
          ),
          eq(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.valuePropId,
          ),
          eq(conversationCallToActionCopyTable.status, input.status),
        ),
      );
    if (!conversationCallToActionCopy[0]) {
      return null;
    }
    return ConversationCallToActionCopy({
      id: conversationCallToActionCopy[0].conversation_call_to_action_copy.id,
      conversationMessageCopyId:
        conversationCallToActionCopy[0].conversation_message_copy.id,
      conversationSubjectCopyId:
        conversationCallToActionCopy[0].conversation_subject_copy.id,
      content:
        conversationCallToActionCopy[0].conversation_call_to_action_copy
          .content,
      type: conversationCallToActionCopy[0].conversation_call_to_action_copy
        .type,
      subjectType:
        conversationCallToActionCopy[0].conversation_subject_copy.type,
      valuePropId:
        conversationCallToActionCopy[0].conversation_subject_copy
          .adSegmentValuePropId,
      messageType:
        conversationCallToActionCopy[0].conversation_message_copy.type,
      status:
        conversationCallToActionCopy[0].conversation_call_to_action_copy.status,
    });
  }

  async deleteManyForLinkedInAdSegmentValueProps(
    input: {
      valuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.valuePropIds.length === 0) {
      return;
    }
    const conversationCallToActionCopyIds = await invoker
      .select({
        id: conversationCallToActionCopyTable.id,
      })
      .from(conversationCallToActionCopyTable)
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          inArray(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.valuePropIds,
          ),
          eq(conversationCallToActionCopyTable.status, input.status),
        ),
      );
    if (conversationCallToActionCopyIds.length === 0) {
      return;
    }
    await invoker.delete(conversationCallToActionCopyTable).where(
      inArray(
        conversationCallToActionCopyTable.id,
        conversationCallToActionCopyIds.map((c) => c.id),
      ),
    );
  }

  async getOneById(
    conversationCallToActionId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationCallToActionCopy | null> {
    const invoker = tx ?? db;
    const conversationCallToActionCopy = await invoker
      .select()
      .from(conversationCallToActionCopyTable)
      .where(
        and(
          eq(conversationCallToActionCopyTable.id, conversationCallToActionId),
          eq(conversationCallToActionCopyTable.status, status),
        ),
      )
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      );
    if (!conversationCallToActionCopy[0]) {
      return null;
    }
    return ConversationCallToActionCopy({
      id: conversationCallToActionCopy[0].conversation_call_to_action_copy.id,
      conversationMessageCopyId:
        conversationCallToActionCopy[0].conversation_message_copy.id,
      conversationSubjectCopyId:
        conversationCallToActionCopy[0].conversation_subject_copy.id,
      content:
        conversationCallToActionCopy[0].conversation_call_to_action_copy
          .content,
      type: conversationCallToActionCopy[0].conversation_call_to_action_copy
        .type,
      subjectType:
        conversationCallToActionCopy[0].conversation_subject_copy.type,
      valuePropId:
        conversationCallToActionCopy[0].conversation_subject_copy
          .adSegmentValuePropId,
      messageType:
        conversationCallToActionCopy[0].conversation_message_copy.type,
      status:
        conversationCallToActionCopy[0].conversation_call_to_action_copy.status,
    });
  }

  async getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationCallToActionCopy[]> {
    const invoker = tx ?? db;
    const conversationCallToActionCopies = await invoker
      .select()
      .from(conversationCallToActionCopyTable)
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationCallToActionCopyTable.status, status),
        ),
      );
    return conversationCallToActionCopies.map((c) =>
      ConversationCallToActionCopy({
        id: c.conversation_call_to_action_copy.id,
        conversationMessageCopyId: c.conversation_message_copy.id,
        conversationSubjectCopyId: c.conversation_subject_copy.id,
        content: c.conversation_call_to_action_copy.content,
        type: c.conversation_call_to_action_copy.type,
        subjectType: c.conversation_subject_copy.type,
        valuePropId: c.conversation_subject_copy.adSegmentValuePropId,
        messageType: c.conversation_message_copy.type,
        status: c.conversation_call_to_action_copy.status,
      }),
    );
  }

  async getAllForValuePropWithMessageAndSubjectCopies(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<
    {
      conversationCallToActionId: string;
      conversationMessageCopyId: string;
      conversationSubjectCopyId: string;
      callToActionCopyContent: string;
      subjectCopyContent: string;
      messageCopyContent: string;
      callToActionType: string;
      subjectType: string;
      messageType: string;
      valuePropId: string;
      leadGenFormUrn?: string | null;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    }[]
  > {
    const invoker = tx ?? db;
    const conversationCallToActionCopies = await invoker
      .select()
      .from(conversationCallToActionCopyTable)
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationCallToActionCopyTable.status, status),
        ),
      );
    return conversationCallToActionCopies.map((c) => ({
      conversationCallToActionId: c.conversation_call_to_action_copy.id,
      conversationMessageCopyId: c.conversation_message_copy.id,
      conversationSubjectCopyId: c.conversation_subject_copy.id,
      callToActionCopyContent: c.conversation_call_to_action_copy.content,
      subjectCopyContent: c.conversation_subject_copy.content,
      messageCopyContent: c.conversation_message_copy.content,
      callToActionType: c.conversation_call_to_action_copy.type,
      subjectType: c.conversation_subject_copy.type,
      messageType: c.conversation_message_copy.type,
      valuePropId: c.conversation_subject_copy.adSegmentValuePropId,
      leadGenFormUrn: c.conversation_subject_copy.leadGenFormUrn,
      status: c.conversation_call_to_action_copy.status,
    }));
  }

  async updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.ids.length === 0) {
      return;
    }
    await invoker
      .update(conversationCallToActionCopyTable)
      .set({ status: "ACTIVE" })
      .where(
        and(
          inArray(conversationCallToActionCopyTable.id, input.ids),
          eq(conversationCallToActionCopyTable.status, "DRAFT"),
        ),
      );
  }

  async deleteManyForValuePropSubjectTypeAndManyMessageTypeAndCallToActionTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    messageType: ConversationMessageCopy["type"],
    callToActionTypes: ConversationCallToActionCopy["type"][],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ) {
    const invoker = tx ?? db;
    if (callToActionTypes.length === 0) {
      return;
    }

    const conversationCallToActionCopyIds = await invoker
      .select({
        id: conversationCallToActionCopyTable.id,
      })
      .from(conversationCallToActionCopyTable)
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationSubjectCopyTable.type, subjectType),
          eq(conversationMessageCopyTable.type, messageType),
          inArray(conversationCallToActionCopyTable.type, callToActionTypes),
          eq(conversationCallToActionCopyTable.status, status),
        ),
      );

    if (conversationCallToActionCopyIds.length === 0) {
      return;
    }

    await invoker.delete(conversationCallToActionCopyTable).where(
      and(
        inArray(
          conversationCallToActionCopyTable.id,
          conversationCallToActionCopyIds.map((c) => c.id),
        ),
      ),
    );
  }

  async deleteManyForValuePropSubjectTypeAndManyMessageTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    messageTypes: ConversationMessageCopy["type"][],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ) {
    const invoker = tx ?? db;

    if (messageTypes.length === 0) {
      return;
    }

    const conversationCallToActionCopyIds = await invoker
      .select({
        id: conversationCallToActionCopyTable.id,
      })
      .from(conversationCallToActionCopyTable)
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationSubjectCopyTable.type, subjectType),
          inArray(conversationMessageCopyTable.type, messageTypes),
          eq(conversationCallToActionCopyTable.status, status),
        ),
      );

    if (conversationCallToActionCopyIds.length === 0) {
      return;
    }

    await invoker.delete(conversationCallToActionCopyTable).where(
      inArray(
        conversationCallToActionCopyTable.id,
        conversationCallToActionCopyIds.map((c) => c.id),
      ),
    );
  }

  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<ConversationCallToActionCopy[]> {
    const invoker = tx ?? db;
    const conversationCallToActionCopies = await invoker
      .select()
      .from(conversationCallToActionCopyTable)
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationCallToActionCopyTable.conversationMessageCopyId,
          conversationMessageCopyTable.id,
        ),
      )
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .innerJoin(
        linkedInAdSegmentValuePropTable,
        eq(
          conversationSubjectCopyTable.adSegmentValuePropId,
          linkedInAdSegmentValuePropTable.id,
        ),
      )
      .where(
        eq(linkedInAdSegmentValuePropTable.linkedInAdSegmentId, adSegmentId),
      );
    return conversationCallToActionCopies.map((c) =>
      ConversationCallToActionCopy({
        id: c.conversation_call_to_action_copy.id,
        conversationMessageCopyId: c.conversation_message_copy.id,
        conversationSubjectCopyId: c.conversation_subject_copy.id,
        content: c.conversation_call_to_action_copy.content,
        type: c.conversation_call_to_action_copy.type,
        subjectType: c.conversation_subject_copy.type,
        valuePropId: c.conversation_subject_copy.adSegmentValuePropId,
        messageType: c.conversation_message_copy.type,
        status: c.conversation_call_to_action_copy.status,
      }),
    );
  }
}
