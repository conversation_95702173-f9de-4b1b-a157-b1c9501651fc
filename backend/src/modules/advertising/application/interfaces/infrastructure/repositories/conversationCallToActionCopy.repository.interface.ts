import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ConversationCallToActionCopy } from "../../../../domain/entites/conversationCallToActionCopy";
import { ConversationCallToActionCopyType } from "../../../../domain/valueObjects/conversationCallToActionCopyType";
import { ConversationMessageCopyType } from "../../../../domain/valueObjects/conversationMessageCopyType";
import { ConversationSubjectCopyType } from "../../../../domain/valueObjects/conversationSubjectCopyType";

export interface IConversationCallToActionCopyRepository {
  createOrUpdateOneIfExists(
    conversationCallToActionCopy: Omit<
      ConversationCallToActionCopy,
      "conversationMessageCopyId" | "conversationSubjectCopyId"
    >,
    tx?: ITransaction,
  ): Promise<ConversationCallToActionCopy>;
  getOne(input: {
    valuePropId: string;
    conversationMessageCopyType: string;
    conversationSubjectCopyType: string;
    conversationCallToActionCopyType: string;
    status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    tx?: ITransaction;
  }): Promise<ConversationCallToActionCopy | null>;
  deleteManyForLinkedInAdSegmentValueProps(
    input: {
      valuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<void>;
  getOneById(
    conversationCallToActionId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationCallToActionCopy | null>;
  getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
  ): Promise<ConversationCallToActionCopy[]>;

  getAllForValuePropWithMessageAndSubjectCopies(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<
    {
      conversationCallToActionId: string;
      conversationMessageCopyId: string;
      conversationSubjectCopyId: string;
      callToActionCopyContent: string;
      subjectCopyContent: string;
      messageCopyContent: string;
      callToActionType: string;
      subjectType: string;
      messageType: string;
      valuePropId: string;
      leadGenFormUrn?: string | null;
    }[]
  >;

  getAllForAdSegment(
    adSegmentId: string,
  ): Promise<ConversationCallToActionCopy[]>;

  updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: ITransaction,
  ): Promise<void>;

  deleteManyForValuePropSubjectTypeAndManyMessageTypeAndCallToActionTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopyType,
    messageType: ConversationMessageCopyType,
    callToActionTypes: ConversationCallToActionCopyType[],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;

  deleteManyForValuePropSubjectTypeAndManyMessageTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopyType,
    messageTypes: ConversationMessageCopyType[],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
}
