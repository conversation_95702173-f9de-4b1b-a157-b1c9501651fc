import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ConversationSubjectCopy } from "../../../../domain/entites/conversationSubjectCopy";

export interface IConversationSubjectCopyRepository {
  createOneOrUpdateOneIfExists(
    input: ConversationSubjectCopy,
    tx?: ITransaction,
  ): Promise<void>;
  getOne(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<ConversationSubjectCopy | null>;

  getId(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<string | null>;

  deleteManyForLinkedInAdSegmentValueProps(
    input: {
      linkedInAdSegmentValuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<void>;

  getOneById(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationSubjectCopy | null>;

  getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationSubjectCopy[]>;

  updateLeadGenFormUrn(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      leadGenFormUrn: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<void>;

  deleteManyForAdSegmentByType(
    input: {
      adSegmentValuePropIds: string[];
      conversationCopyTypes: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<void>;

  getAllForAdSegment(
    adSegmentId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationSubjectCopy[]>;

  updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: ITransaction,
  ): Promise<void>;
}
