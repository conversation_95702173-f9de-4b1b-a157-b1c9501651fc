import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationCopySerivce } from "../../../domain/services/conversationCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { UpdateConversationCopyRequestDto } from "../../dtos/controllerDtos/conversationCopy/updateConversationCopy.dto";

export class UpdateConversationCopyUseCase {
  constructor(
    private readonly conversationMessageCopyService: ConversationMessageCopyService,
    private readonly conversationSubjectCopyService: ConversationSubjectCopyService,
    private readonly conversationCallToActionCopyService: ConversationCallToActionCopyService,
  ) {}

  async execute(input: UpdateConversationCopyRequestDto) {
    await this.conversationSubjectCopyService.createOneOrUpdateOneIfExists({
      content: input.subject,
      type: input.conversationSubjectCopyType,
      valuePropId: input.linkedInAdSegmentValuePropId,
      status: "DRAFT",
    });

    await this.conversationMessageCopyService.createOneOrUpdateOneIfExists({
      content: input.message,
      type: input.conversationMessageCopyType,
      valuePropId: input.linkedInAdSegmentValuePropId,
      subjectType: input.conversationSubjectCopyType,
      status: "DRAFT",
    });

    await this.conversationCallToActionCopyService.createOrUpdateOneIfExists({
      content: input.callToAction,
      type: input.conversationCallToActionCopyType,
      valuePropId: input.linkedInAdSegmentValuePropId,
      subjectType: input.conversationSubjectCopyType,
      messageType: input.conversationMessageCopyType,
      status: "DRAFT",
    });
  }
}
