import { ILLMCompletionService } from "../../../../core/application/interfaces/infrastructure/services/llmCompletionService.interface";
import { IPromptStorageService } from "../../../../core/application/interfaces/infrastructure/services/promptStorage.service.interface";
import { SegmentService } from "../../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../../core/infrastructure/repositories/segment.repository";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInConversationBaseCopyService } from "../../../domain/services/linkedInConversationBaseCopy.service";
import { PositioningService } from "../../../domain/services/positioning.service";
import { GetOrGenerateConversationCopyForCreationFlowRequestDto } from "../../dtos/controllerDtos/conversationCopy/getOrGenerateConversationCopyForCreationFlow.dto";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";

export class GetOrGenerateConversationMessageCopyForCreationFlowUseCase {
  constructor(
    private readonly ctx: {
      conversationSubjectCopyService: ConversationSubjectCopyService;
      conversationMessageCopyService: ConversationMessageCopyService;
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
      llmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
      advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService;
      adSegmentService: AdSegmentService;
      segmentService: SegmentService;
      positioningService: PositioningService;
      organizationId: number;
      conversationBaseCopyService: LinkedInConversationBaseCopyService;
      adSegmentValuePropService: AdSegmentValuePropService;
      adProgramService: LinkedInAdProgramService;
    },
  ) {}

  async *execute(
    input: GetOrGenerateConversationCopyForCreationFlowRequestDto,
  ): AsyncGenerator<{
    subject: string;
    message: string;
    callToAction: string;
    leadGenForm: string | null;
    done: boolean;
  }> {
    console.log("STARTING");

    const adSegmentValueProp = await this.ctx.adSegmentValuePropService.getOne(
      input.linkedInAdSegmentValuePropId,
      "DRAFT",
    );
    if (!adSegmentValueProp) {
      throw new Error("Ad segment value prop not found");
    }
    const adSegment = await this.ctx.adSegmentService.getOne(
      adSegmentValueProp.linkedInAdSegmentId,
    );

    if (!adSegment) {
      throw new Error("Ad segment not found");
    }
    const segment = await this.ctx.segmentService.getSegmentById(
      adSegment.segmentId,
      {
        segmentRepository: segmentRepository,
      },
    );
    if (!segment) {
      throw new Error("Segment not found");
    }
    const adProgram = await this.ctx.adProgramService.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    const positioning = await this.ctx.positioningService.getOne(
      this.ctx.organizationId,
    );

    const baseCopy =
      await this.ctx.conversationBaseCopyService.getConversationBaseCopy(
        adSegment.id,
      );
    if (!baseCopy) {
      throw new Error("Base copy not found");
    }

    const subject = await this.ctx.conversationSubjectCopyService.getOne({
      valuePropId: input.linkedInAdSegmentValuePropId,
      conversationCopyType: input.conversationSubjectCopyType,
      status: "DRAFT",
    });
    console.log("SUBJECT", subject);
    if (subject) {
      const conversationMessageCopy =
        await this.ctx.conversationMessageCopyService.getOne({
          valuePropId: input.linkedInAdSegmentValuePropId,
          messageType: input.conversationMessageCopyType,
          subjectType: subject.type,
          status: "DRAFT",
        });
      console.log("CONVERSATION MESSAGE COPY", conversationMessageCopy);
      if (conversationMessageCopy) {
        const callToAction =
          await this.ctx.conversationCallToActionCopyService.getOne({
            valuePropId: input.linkedInAdSegmentValuePropId,
            conversationCallToActionCopyType:
              input.conversationCallToActionCopyType,
            conversationSubjectCopyType: subject.type,
            conversationMessageCopyType: input.conversationMessageCopyType,
            status: "DRAFT",
          });
        if (callToAction) {
          yield {
            subject: subject.content,
            message: conversationMessageCopy.content,
            leadGenForm: subject.leadGenForm ?? null,
            callToAction: callToAction.content,
            done: true,
          };
        } else {
          console.log("CALL TO ACTION NOT FOUND");
          yield {
            subject: subject.content,
            message: conversationMessageCopy.content,
            callToAction: "",
            leadGenForm: subject.leadGenForm ?? null,
            done: true,
          };
          const callToAction =
            await this.ctx.conversationCallToActionCopyService.createOrUpdateOneIfExists(
              {
                valuePropId: input.linkedInAdSegmentValuePropId,
                type: "standard",
                content: "Want to see the demo?",
                subjectType: subject.type,
                messageType: conversationMessageCopy.type,
                status: "DRAFT",
              },
            );
          yield {
            subject: "",
            message: "",
            callToAction: callToAction.content,
            leadGenForm: subject.leadGenForm ?? null,
            done: true,
          };
        }
      } else {
        console.log("CONVERSATION MESSAGE COPY NOT FOUND");
        let bodyText = "";
        const body =
          this.ctx.advertisingLlmCompletionsService.generateConversationBody(
            {
              adSegmentValueProp: adSegmentValueProp.valueProp,
              baseCopy: baseCopy.baseCopy,
              subject: subject.content,
              positioning: positioning?.content ?? "",
              adTargeting: {
                jobFunction: segment.jobFunction,
                jobSeniority: segment.jobSeniority,
                verticals: segment.verticals,
                annualRevenueLowBound: segment.annualRevenueLowBound,
                annualRevenueHighBound: segment.annualRevenueHighBound,
                numberOfEmployeesLowBound: segment.numberOfEmployeesLowBound,
                numberOfEmployeesHighBound: segment.numberOfEmployeesHighBound,
              },
            },
            {
              lllmCompletions: this.ctx.llmCompletions,
              promptStorage: this.ctx.promptStorage,
            },
          );
        for await (const each of body) {
          yield {
            message: each,
            subject: "",
            callToAction: "",
            leadGenForm: subject.leadGenForm ?? null,
            done: false,
          };
          bodyText += each;
        }
        await this.ctx.conversationMessageCopyService.createOneOrUpdateOneIfExists(
          {
            valuePropId: input.linkedInAdSegmentValuePropId,
            type: input.conversationMessageCopyType,
            content: bodyText,
            subjectType: subject.type,
            status: "DRAFT",
          },
        );

        await this.ctx.conversationCallToActionCopyService.createOrUpdateOneIfExists(
          {
            valuePropId: input.linkedInAdSegmentValuePropId,
            type: "standard",
            content: "Want to see the demo?",
            subjectType: subject.type,
            messageType: input.conversationMessageCopyType,
            status: "DRAFT",
          },
        );

        yield {
          subject: subject.content,
          message: "",
          callToAction: "Want to see the demo?",
          leadGenForm: subject.leadGenForm ?? null,
          done: true,
        };
      }
    } else {
      let subjectContentThing = input.injectedSubject ?? "";
      if (input.injectedSubject) {
        yield {
          subject: input.injectedSubject,
          message: "",
          callToAction: "",
          leadGenForm: adProgram.leadGenForm ?? null,
          done: false,
        };

        await this.ctx.conversationSubjectCopyService.createOneOrUpdateOneIfExists(
          {
            valuePropId: input.linkedInAdSegmentValuePropId,
            type: input.conversationSubjectCopyType,
            content: input.injectedSubject,
            leadGenForm: input.leadGenFormUrn ?? adProgram.leadGenForm,
            status: "DRAFT",
          },
        );
      } else {
        const subjectContent =
          await this.ctx.advertisingLlmCompletionsService.getnerateConversationTitle(
            {
              company: "Not provided",
              adSegmentValueProp: adSegmentValueProp.valueProp,
              subjectType: input.conversationSubjectCopyType,
              adTargeting: {
                jobFunction: segment.jobFunction,
                jobSeniority: segment.jobSeniority,
              },
            },
            {
              lllmCompletions: this.ctx.llmCompletions,
              promptStorage: this.ctx.promptStorage,
            },
          );
        let subjectContentText = "";
        for await (const each of subjectContent) {
          yield {
            subject: each,
            message: "",
            callToAction: "",
            leadGenForm: input.leadGenFormUrn ?? adProgram.leadGenForm ?? null,
            done: false,
          };
          subjectContentText += each;
        }

        console.log("SUBJECT CONTENT TEXT", subjectContentText);
        await this.ctx.conversationSubjectCopyService.createOneOrUpdateOneIfExists(
          {
            valuePropId: input.linkedInAdSegmentValuePropId,
            type: input.conversationSubjectCopyType,
            content: subjectContentText,
            leadGenForm: input.leadGenFormUrn ?? adProgram.leadGenForm,
            status: "DRAFT",
          },
        );
        subjectContentThing = subjectContentText;
      }

      let bodyText = "";
      const body =
        this.ctx.advertisingLlmCompletionsService.generateConversationBody(
          {
            adSegmentValueProp: adSegmentValueProp.valueProp,
            baseCopy: baseCopy.baseCopy,
            subject: subjectContentThing,
            positioning: positioning?.content ?? "",
            adTargeting: {
              jobFunction: segment.jobFunction,
              jobSeniority: segment.jobSeniority,
              verticals: segment.verticals,
              annualRevenueLowBound: segment.annualRevenueLowBound,
              annualRevenueHighBound: segment.annualRevenueHighBound,
              numberOfEmployeesLowBound: segment.numberOfEmployeesLowBound,
              numberOfEmployeesHighBound: segment.numberOfEmployeesHighBound,
            },
          },
          {
            lllmCompletions: this.ctx.llmCompletions,
            promptStorage: this.ctx.promptStorage,
          },
        );
      for await (const each of body) {
        yield {
          message: each,
          subject: "",
          callToAction: "",
          leadGenForm: null,
          done: false,
        };
        bodyText += each;
      }
      await this.ctx.conversationMessageCopyService.createOneOrUpdateOneIfExists(
        {
          valuePropId: input.linkedInAdSegmentValuePropId,
          type: input.conversationMessageCopyType,
          content: bodyText,
          subjectType: input.conversationSubjectCopyType,
          status: "DRAFT",
        },
      );

      await this.ctx.conversationCallToActionCopyService.createOrUpdateOneIfExists(
        {
          valuePropId: input.linkedInAdSegmentValuePropId,
          type: "standard",
          content: "Want to see the demo?",
          subjectType: input.conversationSubjectCopyType,
          messageType: input.conversationMessageCopyType,
          status: "DRAFT",
        },
      );

      yield {
        subject: "",
        message: "",
        callToAction: "Want to see the demo?",
        leadGenForm: adProgram.leadGenForm ?? null,
        done: true,
      };
    }
  }
}
