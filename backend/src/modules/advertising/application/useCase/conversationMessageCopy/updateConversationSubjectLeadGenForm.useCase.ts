import { ConversationSubjectCopy } from "../../../domain/entites/conversationSubjectCopy";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";

export class UpdateConversationSubjectLeadGenFormUseCase {
  constructor(
    private readonly conversationSubjectCopyService: ConversationSubjectCopyService,
  ) {}

  async execute(input: {
    valuePropId: string;
    conversationCopyType: ConversationSubjectCopy["type"];
    leadGenFormUrn: string;
  }) {
    await this.conversationSubjectCopyService.updateLeadGenFormUrn({
      ...input,
      status: "DRAFT",
    });
  }
}
