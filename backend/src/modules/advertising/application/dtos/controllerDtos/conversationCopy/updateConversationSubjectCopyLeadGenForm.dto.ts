import { z } from "zod";

import { conversationSubjectCopyTypeSchema } from "../../../../domain/valueObjects/conversationSubjectCopyType";

export const updateConversationSubjectCopyLeadGenFormRequestDto = z.object({
  valuePropId: z.string(),
  conversationCopyType: z.string(),
  leadGenFormUrn: z.string(),
});

export type UpdateConversationSubjectCopyLeadGenFormRequestDto = z.infer<
  typeof updateConversationSubjectCopyLeadGenFormRequestDto
>;
