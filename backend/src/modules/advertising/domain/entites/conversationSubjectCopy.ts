import { z } from "zod";

import { conversationSubjectCopyTypeSchema } from "../valueObjects/conversationSubjectCopyType";

export const conversationSubjectCopySchema = z.object({
  id: z.string().uuid(),
  valuePropId: z.string().uuid(),
  content: z.string(),
  type: z.string(),
  leadGenForm: z.string().optional().nullable(),
  status: z.enum(["DRAFT", "ACTIVE", "ARCHIVED"]),
});

export const ConversationSubjectCopy = (
  data: z.infer<typeof conversationSubjectCopySchema>,
) => {
  return conversationSubjectCopySchema.parse(data);
};

export type ConversationSubjectCopy = z.infer<
  typeof conversationSubjectCopySchema
>;
