import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IConversationSubjectCopyRepository } from "../../application/interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ConversationSubjectCopy } from "../entites/conversationSubjectCopy";
import { ConversationCallToActionCopyService } from "./conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "./conversationMessageCopy.service";

export class ConversationSubjectCopyService {
  constructor(
    private readonly conversationSubjectCopyRepository: IConversationSubjectCopyRepository,
  ) {}

  async createOneOrUpdateOneIfExists(
    input: Omit<ConversationSubjectCopy, "id">,
    tx?: ITransaction,
  ): Promise<ConversationSubjectCopy> {
    const id = createUuid();
    const data: ConversationSubjectCopy = {
      id: id,
      content: input.content,
      type: input.type,
      valuePropId: input.valuePropId,
      leadGenForm: input.leadGenForm,
      status: input.status,
    };
    await this.conversationSubjectCopyRepository.createOneOrUpdateOneIfExists(
      data,
      tx,
    );
    return ConversationSubjectCopy({
      ...data,
      id,
    });
  }

  async getOne(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    const res = await this.conversationSubjectCopyRepository.getOne(input, tx);
    return res;
  }

  async getId(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    const res = await this.conversationSubjectCopyRepository.getId(input, tx);
    return res;
  }

  async deleteManyForLinkedInAdSegmentValueProps(
    input: {
      valuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    ctx: {
      conversationMessageCopyService: ConversationMessageCopyService;
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
    },
    tx?: ITransaction,
  ) {
    await ctx.conversationMessageCopyService.deleteManyForManyValueProps(
      input.valuePropIds,
      input.status,
      {
        conversationCallToActionCopyService:
          ctx.conversationCallToActionCopyService,
      },
      tx,
    );
    await this.conversationSubjectCopyRepository.deleteManyForLinkedInAdSegmentValueProps(
      {
        linkedInAdSegmentValuePropIds: input.valuePropIds,
        status: input.status,
      },
      tx,
    );
  }

  async getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
  ) {
    const res = await this.conversationSubjectCopyRepository.getAllForValueProp(
      valuePropId,
      status,
    );
    return res;
  }

  async updateLeadGenFormUrn(
    input: {
      valuePropId: string;
      conversationCopyType: string;
      leadGenFormUrn: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    await this.conversationSubjectCopyRepository.updateLeadGenFormUrn(
      input,
      tx,
    );
  }

  async deleteManyForAdSegmentByType(
    input: {
      adSegmentValuePropIds: string[];
      conversationCopyTypes: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    await this.conversationSubjectCopyRepository.deleteManyForAdSegmentByType(
      {
        adSegmentValuePropIds: input.adSegmentValuePropIds,
        conversationCopyTypes: input.conversationCopyTypes,
        status: input.status,
      },
      tx,
    );
  }

  async getAllForAdSegment(
    adSegmentId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
  ) {
    const res = await this.conversationSubjectCopyRepository.getAllForAdSegment(
      adSegmentId,
      status,
    );
    return res;
  }

  async updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: ITransaction,
  ) {
    await this.conversationSubjectCopyRepository.updateManyToActive(input, tx);
  }
}
