import { NonRetriableError } from "inngest";

import { TransactionManagerService } from "../../../../../../core/infrastructure/services/transcationManager.service";
import { InngestJobTriggerPublisher } from "../../../../../../shared/inngestJobTriggerPublisher";
import { StageRepository } from "../../../../../infrastructure/repositories/stage.repository";
import { advertisingInngestClient } from "../../../../../utils/advertisingInngestClient";
import { StartAbTestCommandHandler } from "../../../../internal/commands/startAbTest/startAbTest.command.handler";
import { StartNextAbTestRoundCommandHandler } from "../../../../internal/commands/startNextAbTestRound/startNextAbTestRound.command.handler";
import { startNextAbTestRoundJobTrigger } from "../../../../internal/jobTriggers/startNextAbTestRound.jobTrigger";
import { AbTestRepository } from "../../../../internal/repositories/abTest.repository";
import { AbTestRoundRepository } from "../../../../internal/repositories/abTestRound.repository";
import { AbTestOrchestratorServiceFactory } from "../../../../internal/services/abTestOrchestrator.service";

export const startNextAbTestRoundInngestFunction =
  advertisingInngestClient.createFunction(
    { id: "start-next-ab-test-round" },
    { event: startNextAbTestRoundJobTrigger.NAME },
    async ({ step, event }) => {
      const { abTestId, abTestType } =
        startNextAbTestRoundJobTrigger.schema.parse(event.data);

      const transactionManager = new TransactionManagerService();
      await transactionManager.startTransaction(async (tx) => {
        const orchestrator = AbTestOrchestratorServiceFactory({
          startAbTestCommandHandler: new StartAbTestCommandHandler(
            new AbTestRepository(),
            new StageRepository(),
            new AbTestRoundRepository(),
          ),
          jobTriggerPublisher: InngestJobTriggerPublisher(
            advertisingInngestClient,
          ),
          startNextAbTestRoundCommandHandler:
            new StartNextAbTestRoundCommandHandler({
              abTestRepository: new AbTestRepository(),
              abTestRoundRepository: new AbTestRoundRepository(),
            }),
        });
        const result = await orchestrator(abTestType).handleStartNextRoundEvent(
          abTestId,
          tx,
        );
        if (result.isErr()) {
          throw new NonRetriableError(result.error.type);
        }
      });
    },
  );
