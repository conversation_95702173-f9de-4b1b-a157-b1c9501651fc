import { NonRetriableError } from "inngest";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { TransactionManagerService } from "../../../../../../core/infrastructure/services/transcationManager.service";
import { InngestJobTriggerPublisher } from "../../../../../../shared/inngestJobTriggerPublisher";
import { ConversationCallToActionCopyRepository } from "../../../../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { LinkedInAdAccountRepository } from "../../../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../../../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../../../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInPostRepository } from "../../../../../infrastructure/repositories/linkedInPost.repository";
import { LinkedInSponsoredCreativeRepository } from "../../../../../infrastructure/repositories/linkedInSponsoredCreative.repository";
import { StageRepository } from "../../../../../infrastructure/repositories/stage.repository";
import { LinkedInService } from "../../../../../infrastructure/services/linkedIn.service";
import { SetupLinkedInStateOrchestratorCommandHandler } from "../../../../../linkedInStateOrchestrator/application/commands/setupLinkedInStateOrchestrator/setupLinkedInStateOrchestrator.command.handler";
import { StartLinkedInStateOrchestratorCommandHandler } from "../../../../../linkedInStateOrchestrator/application/commands/startLinkedInStateOrchestrator/startLinkedInStateOrchestrator.command.handler";
import { StopLinkedInStateOrchestratorCommandHandler } from "../../../../../linkedInStateOrchestrator/application/commands/stopLinkedInStateOrchestrator/stopLinkedInStateOrchestrator.command.handler";
import { LinkedInStateOrchestratorService } from "../../../../../linkedInStateOrchestrator/application/linkedInStateOrchestrator.service";
import { GetLinkedInStateOrhestratorMeticsQueryHandler } from "../../../../../linkedInStateOrchestrator/application/queries/getLinkedInStateOrhestratorMetics.query.handler";
import { LinkedInStateConfigRepository } from "../../../../../linkedInStateOrchestrator/repository/linkedInStateConfig.repository";
import { advertisingInngestClient } from "../../../../../utils/advertisingInngestClient";
import { CreateAndProvisionAbTestRoundDayCommandHandler } from "../../../../internal/commands/createAndProvisionAbTestRoundDay/createAndProvisionAbTestRoundDay.command.handler";
import { EndAbTestCommandHandler } from "../../../../internal/commands/endAbTest/endAbTest.command.handler";
import { EndAbTestRoundCommandHandler } from "../../../../internal/commands/endAbTestRound/endAbTestRound.command.handler";
import { EndAbTestRoundDayCommandHandler } from "../../../../internal/commands/endAbTestRoundDay/endAbTestRoundDay.command.hanlder";
import { SetCurrentBestVariantForAbTestHandler } from "../../../../internal/commands/setCurrentBestVariantForAbTest/setCurrentBestVariantForAbTest.command.handler";
import { StartAbTestCommandHandler } from "../../../../internal/commands/startAbTest/startAbTest.command.handler";
import { StartAbTestRoundDayCommandHandler } from "../../../../internal/commands/startAbTestRoundDay/startAbTestRoundDay.command";
import { StartNextAbTestRoundCommandHandler } from "../../../../internal/commands/startNextAbTestRound/startNextAbTestRound.command.handler";
import { DecisionEngine } from "../../../../internal/domain/decisionEngine.domain";
import { startAbTestJobTrigger } from "../../../../internal/jobTriggers/startAbTest.jobTrigger";
import { AbTestRepository } from "../../../../internal/repositories/abTest.repository";
import { AbTestRoundRepository } from "../../../../internal/repositories/abTestRound.repository";
import { AbTestRoundDayRepository } from "../../../../internal/repositories/abTestRoundDay.repository";
import { AbTestRoundDayMetricsRepository } from "../../../../internal/repositories/abTestRoundDayMetrics.repository";
import { DataProviderService } from "../../../../internal/services/abTestDataProviders/dataProvider.service";
import { AbTestOrchestratorServiceFactory } from "../../../../internal/services/abTestOrchestrator.service";

export const startAbTestInngestFunction =
  advertisingInngestClient.createFunction(
    { id: "start-ab-test" },
    { event: startAbTestJobTrigger.NAME },
    async ({ step, event }) => {
      const { stageId, abTestType } = startAbTestJobTrigger.schema.parse(
        event.data,
      );
      const adSegmentRepository = new LinkedInAdSegmentRepository();
      const adProgramRepository = new LinkedInAdProgramRepository();
      const adAccountRepository = new LinkedInAdAccountRepository();
      const stageRepository = new StageRepository();

      const stage = await stageRepository.getStage(stageId);
      if (!stage) {
        throw new Error("Stage not found");
      }

      const adSegment = await adSegmentRepository.getOne(stage.adSegmentId);
      if (!adSegment) {
        throw new Error("AdSegment not found");
      }
      const adProgram = await adProgramRepository.getOne(
        adSegment.linkedInAdProgramId,
      );
      if (!adProgram) {
        throw new Error("AdProgram not found");
      }
      const adAccount = await adAccountRepository.getOneById(
        adProgram.linkedInAdAccountId,
      );
      if (!adAccount) {
        throw new Error("AdAccount not found");
      }

      const dataProviderService = new DataProviderService({
        adSegmentRepository: new LinkedInAdSegmentRepository(),
        adProgramRepository: new LinkedInAdProgramRepository(),
      });

      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        adAccount.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }

      const linkedInService = new LinkedInService(linkedInClient);

      const linkedInStateOrchestratorService =
        new LinkedInStateOrchestratorService({
          setupLinkedInStateOrchestratorCommandHandler:
            new SetupLinkedInStateOrchestratorCommandHandler({
              linkedInStateConfigRepository:
                new LinkedInStateConfigRepository(),
              linkedInCampaignRepository: new LinkedInCampaignRepository(),
              linkedInSponsoredCreativeRepository:
                new LinkedInSponsoredCreativeRepository(),
              linkedInPostRepository: new LinkedInPostRepository(),
              linkedInConversationCallToActionRepository:
                new ConversationCallToActionCopyRepository(),
              linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
              adSegmentRepository: new LinkedInAdSegmentRepository(),
              adProgramRepository: new LinkedInAdProgramRepository(),
              linkedInService: linkedInService,
            }),
          startLinkedInStateOrchestratorCommandHandler:
            new StartLinkedInStateOrchestratorCommandHandler({
              linkedInStateConfigRepository:
                new LinkedInStateConfigRepository(),
              linkedInCampaignRepository: new LinkedInCampaignRepository(),
              linkedInSponsoredCreativeRepository:
                new LinkedInSponsoredCreativeRepository(),
              adSegmentRepository: new LinkedInAdSegmentRepository(),
              adProgramRepository: new LinkedInAdProgramRepository(),
              linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
              linkedInService: linkedInService,
            }),
          stopLinkedInStateOrchestratorCommandHandler:
            new StopLinkedInStateOrchestratorCommandHandler({
              linkedInStateConfigRepository:
                new LinkedInStateConfigRepository(),
              adSegmentRepository: new LinkedInAdSegmentRepository(),
              adProgramRepository: new LinkedInAdProgramRepository(),
              linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
              campaignRepository: new LinkedInCampaignRepository(),
              linkedInCampaignAnalyticsRepository:
                new LinkedInSponsoredCreativeRepository(),
              linkedInService: linkedInService,
            }),
          getLinkedInStateOrchestratorCommandHandler:
            new GetLinkedInStateOrhestratorMeticsQueryHandler(
              new LinkedInStateConfigRepository(),
              new LinkedInAdSegmentRepository(),
              new LinkedInAdProgramRepository(),
              new LinkedInPostRepository(),
              new LinkedInSponsoredCreativeRepository(),
              new ConversationCallToActionCopyRepository(),
            ),
        });

      const transactionManager = new TransactionManagerService();
      await transactionManager.startTransaction(async (tx) => {
        const orchestrator = AbTestOrchestratorServiceFactory({
          startAbTestCommandHandler: new StartAbTestCommandHandler(
            new AbTestRepository(),
            new StageRepository(),
            new AbTestRoundRepository(),
            dataProviderService,
          ),
          jobTriggerPublisher: InngestJobTriggerPublisher(
            advertisingInngestClient,
          ),
          startNextAbTestRoundCommandHandler:
            new StartNextAbTestRoundCommandHandler({
              abTestRepository: new AbTestRepository(),
              abTestRoundRepository: new AbTestRoundRepository(),
            }),
          createAndProvisionRoundDayCommandHandler:
            new CreateAndProvisionAbTestRoundDayCommandHandler(
              new AbTestRoundRepository(),
              dataProviderService,
              new StageRepository(),
              new AbTestRoundDayRepository(),
            ),
          startAbTestRoundDayCommandHandler:
            new StartAbTestRoundDayCommandHandler(
              new AbTestRoundDayRepository(),
            ),
          endAbTestRoundDayCommandHandler: new EndAbTestRoundDayCommandHandler({
            abTestRoundRepository: new AbTestRoundRepository(),
            abTestRoundDayRepository: new AbTestRoundDayRepository(),
            linkedInStateOrchestrator: linkedInStateOrchestratorService,
            dataProviderService: dataProviderService,
            decisionEngine: new DecisionEngine(),
            abTestRoundDayMetricsRepository:
              new AbTestRoundDayMetricsRepository(),
          }),
          endAbTestRoundCommandHandler: new EndAbTestRoundCommandHandler({
            abTestRoundRepository: new AbTestRoundRepository(),
          }),
          endAbTestCommandHandler: new EndAbTestCommandHandler({
            abTestRepository: new AbTestRepository(),
            abTestRoundRepository: new AbTestRoundRepository(),
          }),
          setCurrentBestVariantForAbTestCommandHandler:
            new SetCurrentBestVariantForAbTestHandler(
              new AbTestRepository(),
              new AbTestRoundRepository(),
            ),
          abTestRoundRepository: new AbTestRoundRepository(),
        });
        const result = await orchestrator(abTestType).handleStartAbTestEvent(
          stageId,
          tx,
        );
        if (result.isErr()) {
          throw new NonRetriableError(result.error.type);
        }
      });
    },
  );
