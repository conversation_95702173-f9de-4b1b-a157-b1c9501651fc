import { NonRetriableError } from "inngest";

import { TransactionManagerService } from "../../../../../../core/infrastructure/services/transcationManager.service";
import { InngestJobTriggerPublisher } from "../../../../../../shared/inngestJobTriggerPublisher";
import { LinkedInAdProgramRepository } from "../../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../../../../infrastructure/repositories/linkedInAdSegment.repository";
import { StageRepository } from "../../../../../infrastructure/repositories/stage.repository";
import { advertisingInngestClient } from "../../../../../utils/advertisingInngestClient";
import { CreateAndProvisionAbTestRoundDayCommandHandler } from "../../../../internal/commands/createAndProvisionAbTestRoundDay/createAndProvisionAbTestRoundDay.command.handler";
import { EndAbTestCommandHandler } from "../../../../internal/commands/endAbTest/endAbTest.command.handler";
import { EndAbTestRoundCommandHandler } from "../../../../internal/commands/endAbTestRound/endAbTestRound.command.handler";
import { EndAbTestRoundDayCommandHandler } from "../../../../internal/commands/endAbTestRoundDay/endAbTestRoundDay.command.hanlder";
import { SetCurrentBestVariantForAbTestHandler } from "../../../../internal/commands/setCurrentBestVariantForAbTest/setCurrentBestVariantForAbTest.command.handler";
import { StartAbTestCommandHandler } from "../../../../internal/commands/startAbTest/startAbTest.command.handler";
import { StartAbTestRoundDayCommandHandler } from "../../../../internal/commands/startAbTestRoundDay/startAbTestRoundDay.command";
import { StartNextAbTestRoundCommandHandler } from "../../../../internal/commands/startNextAbTestRound/startNextAbTestRound.command.handler";
import { DecisionEngine } from "../../../../internal/domain/decisionEngine.domain";
import { endAbTestJobTrigger } from "../../../../internal/jobTriggers/endAbTest.jobTrigger";
import { AbTestRepository } from "../../../../internal/repositories/abTest.repository";
import { AbTestRoundRepository } from "../../../../internal/repositories/abTestRound.repository";
import { AbTestRoundDayRepository } from "../../../../internal/repositories/abTestRoundDay.repository";
import { AbTestRoundDayMetricsRepository } from "../../../../internal/repositories/abTestRoundDayMetrics.repository";
import { DataProviderService } from "../../../../internal/services/abTestDataProviders/dataProvider.service";
import { AbTestOrchestratorServiceFactory } from "../../../../internal/services/abTestOrchestrator.service";

export const endAbTestInngestFunction = advertisingInngestClient.createFunction(
  { id: "end-ab-test" },
  { event: endAbTestJobTrigger.NAME },
  async ({ event }) => {
    const { abTestId, abTestType } = endAbTestJobTrigger.schema.parse(
      event.data,
    );

    const transactionManager = new TransactionManagerService();
    await transactionManager.startTransaction(async (tx) => {
      const dataProviderService = new DataProviderService({
        adSegmentRepository: new LinkedInAdSegmentRepository(),
        adProgramRepository: new LinkedInAdProgramRepository(),
      });

      const orchestrator = AbTestOrchestratorServiceFactory({
        startAbTestCommandHandler: new StartAbTestCommandHandler(
          new AbTestRepository(),
          new StageRepository(),
          new AbTestRoundRepository(),
          dataProviderService,
        ),
        jobTriggerPublisher: InngestJobTriggerPublisher(
          advertisingInngestClient,
        ),
        startNextAbTestRoundCommandHandler:
          new StartNextAbTestRoundCommandHandler({
            abTestRepository: new AbTestRepository(),
            abTestRoundRepository: new AbTestRoundRepository(),
          }),
        createAndProvisionRoundDayCommandHandler:
          new CreateAndProvisionAbTestRoundDayCommandHandler(
            new AbTestRoundRepository(),
            dataProviderService,
            new StageRepository(),
            new AbTestRoundDayRepository(),
          ),
        startAbTestRoundDayCommandHandler:
          new StartAbTestRoundDayCommandHandler(new AbTestRoundDayRepository()),
        endAbTestRoundDayCommandHandler: new EndAbTestRoundDayCommandHandler({
          abTestRoundRepository: new AbTestRoundRepository(),
          abTestRoundDayRepository: new AbTestRoundDayRepository(),
          linkedInStateOrchestrator: null as any, // Not needed for endAbTest
          dataProviderService: dataProviderService,
          decisionEngine: new DecisionEngine(),
          abTestRoundDayMetricsRepository:
            new AbTestRoundDayMetricsRepository(),
        }),
        endAbTestRoundCommandHandler: new EndAbTestRoundCommandHandler({
          abTestRoundRepository: new AbTestRoundRepository(),
        }),
        endAbTestCommandHandler: new EndAbTestCommandHandler({
          abTestRepository: new AbTestRepository(),
          abTestRoundRepository: new AbTestRoundRepository(),
        }),
        setCurrentBestVariantForAbTestCommandHandler:
          new SetCurrentBestVariantForAbTestHandler(
            new AbTestRepository(),
            new AbTestRoundRepository(),
          ),
        abTestRoundRepository: new AbTestRoundRepository(),
      });

      const result = await orchestrator(abTestType).handleEndAbTestEvent(
        abTestId,
        tx,
      );
      if (result.isErr()) {
        throw new NonRetriableError(result.error.type);
      }
    });
  },
);
