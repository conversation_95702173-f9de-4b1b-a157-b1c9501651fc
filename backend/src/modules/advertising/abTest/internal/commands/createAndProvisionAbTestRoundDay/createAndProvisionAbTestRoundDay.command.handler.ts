import { err, ok, Result } from "neverthrow";

import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { LinkedInStateOrchestratorService } from "../../../../linkedInStateOrchestrator/application/linkedInStateOrchestrator.service";
import { AbTestRoundDayDomain } from "../../domain/abTestRoundDay.domain";
import { AbTestRoundDay } from "../../domain/abTestRoundDay.entity";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { IAbTestRoundDayRepository } from "../../repositories/abTestRoundDay.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { CreateAndProvisionAbTestRoundDayCommand } from "./createAndProvisionAbTestRoundDay.command.interface";

interface CreateAndProvisionAbTestRoundDayError {
  type:
    | "AB_TEST_ROUND_NOT_RUNNING"
    | "AB_TEST_ROUND_NOT_FOUND"
    | "AB_TEST_NOT_FOUND"
    | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
    | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
    | "UNCOMPLETED_AB_TEST_ROUND_DAYS_ALREADY_EXIST";
}

export class CreateAndProvisionAbTestRoundDayCommandHandler {
  constructor(
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly abTestDataProvider: DataProviderService,
    private readonly stageRepository: IStageRepository,
    private readonly abTestRoundDayRepository: IAbTestRoundDayRepository,
  ) {}

  async execute(
    command: CreateAndProvisionAbTestRoundDayCommand,
  ): Promise<Result<AbTestRoundDay, CreateAndProvisionAbTestRoundDayError>> {
    const abTestRound = await this.abTestRoundRepository.getOne(
      command.abTestRoundId,
      command.type,
      command.tx,
    );
    if (!abTestRound) {
      return err({ type: "AB_TEST_ROUND_NOT_FOUND" });
    }
    if (abTestRound.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_ROUND_NOT_RUNNING" });
    }

    const existingAbTestRoundDays =
      await this.abTestRoundDayRepository.getAllForAbTestRound(
        command.abTestRoundId,
        command.type,
        command.tx,
      );

    const stage = await this.stageRepository.getStage(abTestRound.abTestId);
    if (!stage) {
      return err({ type: "AB_TEST_NOT_FOUND" });
    }

    const adSegmentId = stage.adSegmentId;
    const input =
      await this.abTestDataProvider.getLinkedInStateOrchestratorInput({
        adSegmentId: adSegmentId,
        abTestType: command.type,
        currentBestVariantId: abTestRound.currentBestId,
        contenderVariantId: abTestRound.contenderId,
      });
    if (input.isErr()) {
      return err({ type: input.error.type });
    }

    const linkedInStateConfig =
      await this.linkedInStateOrchestrator.setupLinkedInState(
        input.value,
        command.tx,
      );

    const abTestRoundDay = AbTestRoundDayDomain.create({
      abTestRound,
      deploymentConfigId: linkedInStateConfig.id,
      existingAbTestRoundDays: existingAbTestRoundDays,
    });
    if (abTestRoundDay.isErr()) {
      return err({ type: abTestRoundDay.error.type });
    }

    await this.abTestRoundDayRepository.createOne(
      abTestRoundDay.value,
      command.type,
      command.tx,
    );

    return ok(abTestRoundDay.value);
  }
}
