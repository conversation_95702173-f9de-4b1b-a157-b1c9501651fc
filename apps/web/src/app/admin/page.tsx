import { auth } from "@clerk/nextjs/server";

import { AdminOrganizationTable } from "./adminOrganizationTable";

export default async function AdminPage() {
  const user = auth();

  if (
    user.userId != "user_2hpfJ7nV9VwkpT8o4pmV3vkLsd0" &&
    user.userId != "user_2gvKecrxU1ugrxInkMeeqc4KJsp" &&
    user.userId != "user_2p2YdEOYsl2oCmgvKrj8AWAf5Td"
  ) {
    return null;
  }

  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      <AdminOrganizationTable />
    </div>
  );
}
